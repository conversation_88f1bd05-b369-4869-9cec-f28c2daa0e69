package examples

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	rank2 "kairo_paradise_server/services/public/internal/module/rank"
	"time"
)

// ExampleUsage 展示如何使用排行榜系统
func ExampleUsage() {
	logger.Info("Starting rank system example")

	// 获取排行榜管理器
	manager := rank2.GetRankManager()

	// 初始化管理器
	err := manager.Initialize()
	if err != nil {
		logger.Error("Failed to initialize rank manager", zap.Error(err))
		return
	}
	defer manager.Stop()

	ctx := context.Background()

	// 示例1：更新排行榜条目
	logger.Info("Example 1: Updating rank entries")

	// 创建一些测试玩家数据
	testPlayers := []*rank2.Entry{
		rank2.NewEntry(1001, "Player1", 10, "avatar1.jpg", 1500),
		rank2.NewEntry(1002, "Player2", 15, "avatar2.jpg", 2000),
		rank2.<PERSON>Entry(1003, "Player3", 12, "avatar3.jpg", 1800),
		rank2.<PERSON><PERSON><PERSON><PERSON>(1004, "Player4", 8, "avatar4.jpg", 1200),
		rank2.NewEntry(1005, "Player5", 20, "avatar5.jpg", 2500),
	}

	// 更新繁荣榜（rankType = 2）
	for _, player := range testPlayers {
		err := manager.UpdateRankEntry(ctx, 2, player)
		if err != nil {
			logger.Error("Failed to update rank entry",
				zap.Error(err),
				zap.Uint64("playerID", player.PlayerInfo.PlayerID))
		} else {
			logger.Info("Updated rank entry",
				zap.Uint64("playerID", player.PlayerInfo.PlayerID),
				zap.Int32("prosperity", player.RankEntry.Score))
		}
	}

	// 等待队列处理完成
	time.Sleep(2 * time.Second)

	// 示例2：获取排行榜列表
	logger.Info("Example 2: Getting rank list")

	entries, totalCount, err := manager.GetRankList(ctx, 2, 1, 10)
	if err != nil {
		logger.Error("Failed to get rank list", zap.Error(err))
	} else {
		logger.Info("Got rank list",
			zap.Int("entryCount", len(entries)),
			zap.Int64("totalCount", totalCount))

		for i, entry := range entries {
			logger.Info("Rank entry",
				zap.Int("rank", i+1),
				zap.Uint64("playerID", entry.PlayerInfo.PlayerID),
				zap.String("playerName", entry.PlayerInfo.PlayerName),
				zap.Int32("prosperity", entry.RankEntry.Score))
		}
	}

	// 示例3：获取特定玩家排名
	logger.Info("Example 3: Getting player rank")

	rank, entry, err := manager.GetPlayerRank(ctx, 2, 1002)
	if err != nil {
		logger.Error("Failed to get player rank", zap.Error(err))
	} else if entry == nil {
		logger.Info("Player not in rank", zap.Uint64("playerID", 1002))
	} else {
		logger.Info("Player rank",
			zap.Uint64("playerID", 1002),
			zap.Int32("rank", rank),
			zap.Int32("prosperity", entry.RankEntry.Score))
	}

	// 示例4：获取队列统计
	logger.Info("Example 4: Getting queue stats")

	allStats := manager.GetAllQueueStats()
	for rankType, stats := range allStats {
		logger.Info("Queue statistics",
			zap.Int32("rankType", rankType),
			zap.Int32("queueSize", stats.QueueSize),
			zap.Int64("processedCount", stats.ProcessedCount),
			zap.Int64("errorCount", stats.ErrorCount),
			zap.Int32("workerCount", stats.WorkerCount))
	}

	// 示例5：压力测试
	logger.Info("Example 5: Stress test")

	startTime := time.Now()
	updateCount := 1000

	for i := 0; i < updateCount; i++ {
		playerID := uint64(2000 + i)
		prosperity := int32(1000 + i*10)

		entry := rank2.NewEntry(
			playerID,
			fmt.Sprintf("TestPlayer%d", i),
			int32(10+i%20),
			fmt.Sprintf("test_avatar%d.jpg", i),
			prosperity,
		)

		err := manager.UpdateRankEntry(ctx, 2, entry)
		if err != nil {
			logger.Error("Failed to update rank entry in stress test",
				zap.Error(err),
				zap.Uint64("playerID", playerID))
		}
	}

	duration := time.Since(startTime)
	logger.Info("Stress test completed",
		zap.Int("updateCount", updateCount),
		zap.Duration("duration", duration),
		zap.Float64("updatesPerSecond", float64(updateCount)/duration.Seconds()))

	// 等待队列处理完成
	time.Sleep(5 * time.Second)

	// 获取最终统计
	finalStats := manager.GetAllQueueStats()
	for rankType, stats := range finalStats {
		logger.Info("Final queue statistics",
			zap.Int32("rankType", rankType),
			zap.Int32("queueSize", stats.QueueSize),
			zap.Int64("processedCount", stats.ProcessedCount),
			zap.Int64("errorCount", stats.ErrorCount))
	}

	logger.Info("Rank system example completed")
}

// CreateTestConfig 创建测试配置
func CreateTestConfig() *rank2.Config {
	return &rank2.Config{
		RankType:       2,    // 繁荣榜
		MinScoreLimit:  100,  // 最低100分上榜
		MaxQueryLimit:  500,  // 最多查询500条
		ShowRankLimit:  300,  // 显示前300名
		MaxRankLimit:   1000, // 最多1000名
		UpdateInterval: 0,    // 实时更新
		IsRealtime:     true, // 实时排行榜
		QueueSize:      2000, // 队列大小2000
		WorkerCount:    3,    // 3个工作器
		BatchSize:      20,   // 批处理大小20
	}
}

// BenchmarkRankSystem 性能基准测试
func BenchmarkRankSystem(playerCount int, updateCount int) {
	logger.Info("Starting rank system benchmark",
		zap.Int("playerCount", playerCount),
		zap.Int("updateCount", updateCount))

	manager := rank2.GetRankManager()

	// 如果未初始化，先初始化
	if !manager.IsInitialized() {
		err := manager.Initialize()
		if err != nil {
			logger.Error("Failed to initialize rank manager", zap.Error(err))
			return
		}
	}

	ctx := context.Background()

	// 创建测试玩家
	players := make([]*rank2.Entry, playerCount)
	for i := 0; i < playerCount; i++ {
		players[i] = rank2.NewEntry(
			uint64(10000+i),
			fmt.Sprintf("BenchPlayer%d", i),
			int32(1+i%50),
			fmt.Sprintf("bench_avatar%d.jpg", i),
			int32(100+i*5),
		)
	}

	// 基准测试：批量更新
	logger.Info("Benchmark: Batch updates")
	startTime := time.Now()

	for i := 0; i < updateCount; i++ {
		player := players[i%playerCount]
		// 随机增加繁荣度
		player.RankEntry.Score += int32(i % 100)

		err := manager.UpdateRankEntry(ctx, 2, player)
		if err != nil {
			logger.Error("Benchmark update failed", zap.Error(err))
		}
	}

	updateDuration := time.Since(startTime)

	// 等待队列处理完成
	time.Sleep(3 * time.Second)

	// 基准测试：批量查询
	logger.Info("Benchmark: Batch queries")
	queryStartTime := time.Now()

	queryCount := 100
	for i := 0; i < queryCount; i++ {
		_, _, err := manager.GetRankList(ctx, 2, 1, 50)
		if err != nil {
			logger.Error("Benchmark query failed", zap.Error(err))
		}
	}

	queryDuration := time.Since(queryStartTime)

	// 输出基准测试结果
	logger.Info("Benchmark results",
		zap.Int("playerCount", playerCount),
		zap.Int("updateCount", updateCount),
		zap.Duration("updateDuration", updateDuration),
		zap.Float64("updatesPerSecond", float64(updateCount)/updateDuration.Seconds()),
		zap.Int("queryCount", queryCount),
		zap.Duration("queryDuration", queryDuration),
		zap.Float64("queriesPerSecond", float64(queryCount)/queryDuration.Seconds()))

	// 获取最终统计
	stats := manager.GetAllQueueStats()
	for rankType, stat := range stats {
		logger.Info("Benchmark queue stats",
			zap.Int32("rankType", rankType),
			zap.Int64("processedCount", stat.ProcessedCount),
			zap.Int64("errorCount", stat.ErrorCount),
			zap.Float64("errorRate", float64(stat.ErrorCount)/float64(stat.ProcessedCount)*100))
	}
}
