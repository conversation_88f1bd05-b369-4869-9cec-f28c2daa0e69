package rank

import (
	"go.uber.org/zap"
	"kairo_paradise_server/internal/logger"
	"sync"
	"time"
)

// PlayerInfoManager 玩家信息管理器（公共数据存储）
type PlayerInfoManager struct {
	// 玩家基础信息映射
	playerInfoMap map[uint64]*PlayerInfo
	// 读写锁
	mutex sync.RWMutex
	// 统计信息
	totalPlayers int64
	updateCount  int64
}

// NewPlayerInfoManager 创建新的玩家信息管理器
func NewPlayerInfoManager() *PlayerInfoManager {
	return &PlayerInfoManager{
		playerInfoMap: make(map[uint64]*PlayerInfo),
		totalPlayers:  0,
		updateCount:   0,
	}
}

// UpdatePlayerInfo 更新玩家基础信息
func (pim *PlayerInfoManager) UpdatePlayerInfo(playerInfo *PlayerInfo) {
	if playerInfo == nil || playerInfo.PlayerID == 0 {
		logger.Warn("Invalid player info", zap.Uint64("playerID", playerInfo.PlayerID))
		return
	}

	pim.mutex.Lock()
	defer pim.mutex.Unlock()

	// 检查是否是新玩家
	isNewPlayer := false
	if _, exists := pim.playerInfoMap[playerInfo.PlayerID]; !exists {
		isNewPlayer = true
		pim.totalPlayers++
	}

	// 更新时间戳
	playerInfo.UpdateTime = time.Now().Unix()

	// 存储玩家信息
	pim.playerInfoMap[playerInfo.PlayerID] = playerInfo
	pim.updateCount++

	if isNewPlayer {
		logger.Debug("New player info added",
			zap.Uint64("playerID", playerInfo.PlayerID),
			zap.String("playerName", playerInfo.PlayerName),
			zap.Int32("level", playerInfo.Level))
	} else {
		logger.Debug("Player info updated",
			zap.Uint64("playerID", playerInfo.PlayerID),
			zap.String("playerName", playerInfo.PlayerName),
			zap.Int32("level", playerInfo.Level))
	}
}

// GetPlayerInfo 获取玩家基础信息
func (pim *PlayerInfoManager) GetPlayerInfo(playerID uint64) *PlayerInfo {
	playerInfo, exists := pim.playerInfoMap[playerID]
	if !exists {
		return nil
	}

	// 返回副本以避免外部修改
	return &PlayerInfo{
		PlayerID:   playerInfo.PlayerID,
		PlayerName: playerInfo.PlayerName,
		Level:      playerInfo.Level,
		Avatar:     playerInfo.Avatar,
		UpdateTime: playerInfo.UpdateTime,
	}
}

// GetPlayerInfoBatch 批量获取玩家基础信息
func (pim *PlayerInfoManager) GetPlayerInfoBatch(playerIDs []uint64) map[uint64]*PlayerInfo {
	result := make(map[uint64]*PlayerInfo)
	for _, playerID := range playerIDs {
		if playerInfo, exists := pim.playerInfoMap[playerID]; exists {
			// 返回副本以避免外部修改
			result[playerID] = &PlayerInfo{
				PlayerID:   playerInfo.PlayerID,
				PlayerName: playerInfo.PlayerName,
				Level:      playerInfo.Level,
				Avatar:     playerInfo.Avatar,
				UpdateTime: playerInfo.UpdateTime,
			}
		}
	}

	return result
}

// HasPlayerInfo 检查玩家信息是否存在
func (pim *PlayerInfoManager) HasPlayerInfo(playerID uint64) bool {
	_, exists := pim.playerInfoMap[playerID]
	return exists
}

// RemovePlayerInfo 移除玩家信息
func (pim *PlayerInfoManager) RemovePlayerInfo(playerID uint64) bool {
	pim.mutex.Lock()
	defer pim.mutex.Unlock()

	if _, exists := pim.playerInfoMap[playerID]; exists {
		delete(pim.playerInfoMap, playerID)
		pim.totalPlayers--

		logger.Debug("Player info removed", zap.Uint64("playerID", playerID))
		return true
	}

	return false
}

// ClearAll 清空所有玩家信息
func (pim *PlayerInfoManager) ClearAll() {
	pim.mutex.Lock()
	defer pim.mutex.Unlock()

	pim.playerInfoMap = make(map[uint64]*PlayerInfo)
	pim.totalPlayers = 0

	logger.Info("All player info cleared")
}

// GetAllPlayerIDs 获取所有玩家ID
func (pim *PlayerInfoManager) GetAllPlayerIDs() []uint64 {
	playerIDs := make([]uint64, 0, len(pim.playerInfoMap))
	for playerID := range pim.playerInfoMap {
		playerIDs = append(playerIDs, playerID)
	}

	return playerIDs
}

// GetPlayerCount 获取玩家总数
func (pim *PlayerInfoManager) GetPlayerCount() int64 {
	pim.mutex.RLock()
	defer pim.mutex.RUnlock()

	return pim.totalPlayers
}

// GetUpdateCount 获取更新次数
func (pim *PlayerInfoManager) GetUpdateCount() int64 {
	pim.mutex.RLock()
	defer pim.mutex.RUnlock()

	return pim.updateCount
}

// CleanupOldPlayers 清理长时间未更新的玩家信息
func (pim *PlayerInfoManager) CleanupOldPlayers(maxAge int64) int {
	pim.mutex.Lock()
	defer pim.mutex.Unlock()

	currentTime := time.Now().Unix()
	cleanupCount := 0

	for playerID, playerInfo := range pim.playerInfoMap {
		if currentTime-playerInfo.UpdateTime > maxAge {
			delete(pim.playerInfoMap, playerID)
			pim.totalPlayers--
			cleanupCount++
		}
	}

	if cleanupCount > 0 {
		logger.Info("Cleaned up old player info",
			zap.Int("cleanupCount", cleanupCount),
			zap.Int64("maxAge", maxAge))
	}

	return cleanupCount
}
