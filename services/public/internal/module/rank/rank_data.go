package rank

import (
	"kairo_paradise_server/services/pb"
	"time"
)

// Config represents the configuration for a leaderboard
type Config struct {
	RankType       int32 // 排行榜类型，对应 ERankType
	MinScoreLimit  int32 // 最低分数限制，低于此分数不能上榜
	MaxQueryLimit  int32 // 查询人数上限
	ShowRankLimit  int32 // 榜上显示数量
	MaxRankLimit   int32 // 排行数量
	UpdateInterval int32 // 更新间隔（秒），0表示实时更新
	IsRealtime     bool  // 是否为实时排行榜

	// 队列配置
	QueueSize   int32 // 队列大小
	WorkerCount int32 // 工作器数量
	BatchSize   int32 // 批处理大小
}

// PlayerInfo represents basic player information (shared across all ranks)
type PlayerInfo struct {
	PlayerID   uint64 // 玩家ID
	PlayerName string // 玩家名称
	Level      int32  // 玩家等级
	Icon       int32  // 玩家头像
	UpdateTime int64  // 分数更新时间
	Prosperity int32  // 玩家繁荣度
}

// RankEntry represents an entry in a specific leaderboard (only rank-specific data)
type RankEntry struct {
	PlayerID   uint64 // 玩家ID
	Score      int32  // 分数（如繁荣度、充值金额等）
	UpdateTime int64  // 分数更新时间
	Ranking    int32  // 排名
}

// Entry represents a complete leaderboard entry (combines PlayerInfo and RankEntry)
type Entry struct {
	PlayerInfo *PlayerInfo // 玩家基础信息
	RankEntry  *RankEntry
}

// ToProto converts an Entry to a protobuf RankEntryData
func (e *Entry) ToProto() *pb.RankEntryData {
	if e.PlayerInfo == nil || e.RankEntry == nil {
		return nil
	}
	return &pb.RankEntryData{
		PlayerId:   &e.PlayerInfo.PlayerID,
		PlayerName: &e.PlayerInfo.PlayerName,
		Level:      &e.PlayerInfo.Level,
		Prosperity: &e.RankEntry.Score,
	}
}

// NewEntryFromProto creates a new Entry from a protobuf RankEntryData
func NewEntryFromProto(data *pb.RankEntryData) *Entry {
	if data == nil {
		return nil
	}

	playerInfo := &PlayerInfo{
		UpdateTime: time.Now().Unix(),
	}
	if data.PlayerId != nil {
		playerInfo.PlayerID = *data.PlayerId
	}
	if data.PlayerName != nil {
		playerInfo.PlayerName = *data.PlayerName
	}
	if data.Level != nil {
		playerInfo.Level = *data.Level
	}

	rankEntry := &RankEntry{
		PlayerID:   playerInfo.PlayerID,
		UpdateTime: time.Now().Unix(),
	}
	if data.Prosperity != nil {
		rankEntry.Score = *data.Prosperity
	}

	return &Entry{
		PlayerInfo: playerInfo,
		RankEntry:  rankEntry,
	}
}

// NewEntry creates a new Entry with separate PlayerInfo and RankEntry
func NewEntry(playerID uint64, playerName string, level int32, icon int32, score int32) *Entry {
	return &Entry{
		PlayerInfo: &PlayerInfo{
			PlayerID:   playerID,
			PlayerName: playerName,
			Level:      level,
			Icon:       icon,
			UpdateTime: time.Now().Unix(),
		},
		RankEntry: &RankEntry{
			PlayerID:   playerID,
			Score:      score,
			UpdateTime: time.Now().Unix(),
		},
	}
}

// UpdateRequest represents a rank update request
type UpdateRequest struct {
	RankType int32  // 排行榜类型
	Entry    *Entry // 排行榜条目
	Priority int32  // 优先级，数值越小优先级越高
}

// UpdateResult represents the result of a rank update
type UpdateResult struct {
	PlayerID uint64 // 玩家ID
	RankType int32  // 排行榜类型
	NewRank  int32  // 新排名，-1表示未上榜
	OldRank  int32  // 旧排名，-1表示之前未上榜
	Success  bool   // 是否成功
	Error    error  // 错误信息
}

// QueueStats represents queue statistics
type QueueStats struct {
	QueueSize      int32 // 当前队列大小
	ProcessedCount int64 // 已处理数量
	ErrorCount     int64 // 错误数量
	WorkerCount    int32 // 工作器数量
}

// MemoryRankData represents in-memory rank data (only stores rank-specific data)
type MemoryRankData struct {
	Entries    []*RankEntry          // 排行榜条目列表（按分数排序）
	PlayerMap  map[uint64]*RankEntry // 玩家ID到排行榜条目的映射
	TotalCount int64                 // 总条目数
	UpdateTime int64                 // 最后更新时间
}

// NewMemoryRankData creates a new MemoryRankData
func NewMemoryRankData() *MemoryRankData {
	return &MemoryRankData{
		Entries:    make([]*RankEntry, 0),
		PlayerMap:  make(map[uint64]*RankEntry),
		TotalCount: 0,
		UpdateTime: time.Now().Unix(),
	}
}
